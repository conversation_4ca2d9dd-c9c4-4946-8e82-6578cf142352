package exr

import (
	"context"
	"fmt"
	"github.com/gorhill/cronexpr"
	. "mysql_archive/pkg/config"
	pubconn "mysql_archive/pkg/conn"
	"mysql_archive/pkg/zlog"
	"mysql_archive/src/task/day"
	"mysql_archive/src/task/row"
	sync2 "mysql_archive/src/task/sync"
	"strconv"
	"sync"
	"time"
)

type Scheduler struct {
	scheduleTable map[string]*JobInfo
	updateChan    chan map[string]*JobInfo
	stopChans     map[string]chan struct{}
	mu            sync.Mutex
}

func ConvertToJobDetail(jobs map[string]map[string]string) map[string]JobDetail {
	result := make(map[string]JobDetail)
	for jobName, details := range jobs {
		// 使用默认的cron表达式，如果配置中没有expr字段
		exprStr := details["expr"]
		if exprStr == "" {
			exprStr = "0 15 * * *" // 默认每天15:00执行
		}
		expr, err := cronexpr.Parse(exprStr)
		if err != nil {
			fmt.Printf("Error parsing cron expression for job %s: %v\n", jobName, err)
			continue
		}
		batch, _ := strconv.Atoi(details["batch"])
		interval, _ := strconv.Atoi(details["interval"])

		// 使用任务配置中的c_mode字段，每个任务独立设置执行模式
		cmode := details["c_mode"]
		if cmode == "" {
			zlog.Error("任务配置中缺少c_mode字段，任务名:", jobName)
			continue // 跳过没有配置执行模式的任务
		}
		zlog.Debug("任务配置转换，任务名:", jobName, "使用模式:", cmode)

		jobDetail := JobDetail{
			PRIMARY:    details["PRIMARY"],
			Batch:      batch,
			CMode:      cmode,
			ColumnType: details["cloumtype"],
			Conditions: details["conditions"],
			DateColumn: details["datecloum"],
			Expr:       expr,
			Interval:   interval,
			DBName:     details["dbname"],
		}
		result[jobName] = jobDetail
	}
	return result
}

func GenerateSchedule(jobs map[string]map[string]string) map[string]*JobInfo {
	convertedJobs := ConvertToJobDetail(jobs)
	scheduleTable := make(map[string]*JobInfo)
	for jobName, jobDetail := range convertedJobs {
		// 设置任务立即执行，便于验证
		nextTime := time.Now().Add(-1 * time.Second) // 设置为1秒前，确保立即执行
		cronJob := &CronJob{
			JobName:  jobName,
			Expr:     jobDetail.Expr,
			NextTime: nextTime,
		}
		zlog.Debug("【关键点】生成任务调度信息，任务名:", jobName, "模式:", jobDetail.CMode, "下次执行时间:", nextTime.Format("2006-01-02 15:04:05"))
		// 读取ddl
		if ddl, ok := Role.Load(jobName); ok {
			scheduleTable[jobName] = &JobInfo{
				JobDetail: jobDetail,
				DDl:       fmt.Sprintf("%s", ddl), // You can set the DDL value here if needed
				CronJob:   cronJob,
			}
		}
	}
	return scheduleTable
}

const maxConcurrentTasks = 5 // 您可以根据需要更改此值

var (
	r row.Row
	d day.Day
	s sync2.Sync
)

func init() {
	// 先创建连接对象
	Conn := &pubconn.Conn{
		Db: nil, // 初始为nil，等待连接建立
	}

	// 启动数据库连接检查和维护协程
	go func(Conn *pubconn.Conn) {
		// 首先等待数据库连接建立
		for {
			zlog.Info("正在尝试建立数据库连接...")
			if db, err := pubconn.Init(*Mysqldb); err != nil {
				zlog.Error("数据库连接失败，30秒后重试，错误:", err.Error())
				time.Sleep(30 * time.Second) // 连接失败时等待30秒
				continue
			} else {
				Conn.Db = db
				zlog.Info("数据库连接建立成功，开始正常运行")
				break
			}
		}

		// 数据库连接建立后，开始正常的维护循环
		for {
			// 检查并维护数据库连接（每5秒检查一次）
			Conn.LoopConn()

			zlog.Debug("【定时任务】开始定期进度更新检查")
			// 定期更新所有表的进度
			updateCount := 0
			for tableName, _ := range Progress {
				if tableName != "" && Re.Running {
					// 只更新进行中的表（进度小于100的表）
					if progress, ok := Progress[tableName]; ok && progress < 100.0 {
						updateCount++
						dbName := "gstation" // 默认数据库名
						// 尝试从配置中获取数据库名
						if tableConfig, exists := Backinfo[tableName]; exists {
							if dbNameFromConfig, hasDB := tableConfig["dbname"]; hasDB {
								dbName = dbNameFromConfig
							}
						}
						// 更新进度 - 使用改进的更新逻辑
						zlog.Debug("【自动更新】==================== 开始自动更新 ====================")
						zlog.Debug("【自动更新】表名:", tableName, "当前进度:", progress, "%")
						zlog.Debug("【自动更新】数据库名:", dbName)

						// 简单的防回退逻辑：只允许进度增长
						zlog.Debug("【自动更新】开始调用 TaskProgress 计算新进度...")
						newProgress := Conn.TaskProgress(tableName, dbName)
						zlog.Debug("【自动更新】TaskProgress 调用完成，返回结果:", newProgress, "%")

						zlog.Debug("【自动更新】计算完成，表名:", tableName, "新进度:", newProgress, "% 当前进度:", progress, "%")

						// 严格的单调递增检查
						if newProgress > 0 && newProgress <= 100.0 && newProgress > progress {
							oldProgress := Progress[tableName]
							Progress[tableName] = newProgress
							zlog.Debug("【自动更新】 进度已更新，表名:", tableName, "旧进度:", oldProgress, "% → 新进度:", newProgress, "%")
						} else if newProgress == progress {
							zlog.Debug("【自动更新】➡  进度无变化，表名:", tableName, "进度:", progress, "%")
						} else if newProgress < progress {
							zlog.Error("【自动更新】    检测到进度回退，拒绝更新！")
							zlog.Error("【自动更新】    表名:", tableName)
							zlog.Error("【自动更新】    当前进度:", progress, "%")
							zlog.Error("【自动更新】    计算进度:", newProgress, "%")
							zlog.Error("【自动更新】    回退幅度:", progress-newProgress, "%")
							zlog.Error("【自动更新】    原因: 统计信息不稳定，保持当前进度")
						} else {
							zlog.Debug("【自动更新】 跳过进度更新，表名:", tableName, "计算进度:", newProgress, "% 原因: 进度值无效")
						}

						zlog.Debug("【自动更新】==================== 自动更新结束 ====================")
					}
				}
			}

			if updateCount > 0 {
				zlog.Debug("【定时任务】本轮更新了", updateCount, "个表的进度")
			} else {
				zlog.Debug("【定时任务】本轮无需更新进度（所有表都已完成或系统未运行）")
			}
			zlog.Debug("【定时任务】定期进度更新检查完成，等待5秒")

			time.Sleep(5 * time.Second)
		}
	}(Conn)

	r = row.Row{Conn: Conn}
	d = day.Day{Conn: Conn}
	s = sync2.Sync{Conn: Conn}
}

func Metrics(JobInfo *JobInfo) {
	zlog.Debug("【关键点】进入Metrics函数，任务名:", JobInfo.CronJob.JobName, "执行模式:", JobInfo.JobDetail.CMode)

	// 检查数据库连接状态，如果连接不可用则跳过任务执行
	if !r.Conn.IsConnected() {
		zlog.Warn("【跳过任务】数据库连接不可用，跳过任务执行:", JobInfo.CronJob.JobName)
		return
	}

	zlog.Debug("【任务详情】", JobInfo.JobDetail)
	zlog.Debug("【任务时间】当前时间:", time.Now().Format("2006-01-02 15:04:05"), "下次执行时间:", JobInfo.CronJob.NextTime.Format("2006-01-02 15:04:05"))

	switch JobInfo.JobDetail.CMode {
	case "row":
		zlog.Debug("【关键点】开始执行row模式任务:", JobInfo.CronJob.JobName)
		// 只有当进度不存在时才初始化为0，避免重置正在进行的任务
		if _, exists := Progress[JobInfo.CronJob.JobName]; !exists {
			Progress[JobInfo.CronJob.JobName] = 0.0
			zlog.Debug("初始化进度为0，表名:", JobInfo.CronJob.JobName)
		} else {
			zlog.Debug("保持现有进度，表名:", JobInfo.CronJob.JobName, "当前进度:", Progress[JobInfo.CronJob.JobName])
		}
		// 记录任务执行前的进度
		progressBeforeTask := Progress[JobInfo.CronJob.JobName]
		zlog.Debug("【关键点】任务执行前进度:", JobInfo.CronJob.JobName, "进度:", progressBeforeTask, "%")

		r.Start(JobInfo)
		zlog.Debug("【关键点】row模式任务执行完成:", JobInfo.CronJob.JobName)

		// 检查任务执行后的进度
		progressAfterTask := Progress[JobInfo.CronJob.JobName]
		zlog.Debug("【关键点】任务执行后进度:", JobInfo.CronJob.JobName, "进度:", progressAfterTask, "%")

		// 如果任务执行过程中已经设置为100%（比如无数据需要归档），则不再重新计算
		if progressAfterTask == 100.0 {
			zlog.Debug("【关键点】任务已设置为100%完成，原因: 无数据需要归档或数据过新，表名:", JobInfo.CronJob.JobName)
			zlog.Debug("【关键点】保持100%进度，不进行重新计算")
		} else {
			// 任务完成后检查是否真正完成归档
			finalProgress := r.Conn.TaskProgress(JobInfo.CronJob.JobName, JobInfo.JobDetail.DBName)
			zlog.Debug("【关键点】任务完成后进度检查，表名:", JobInfo.CronJob.JobName, "计算进度:", finalProgress, "%")

			// 只有当真正没有数据需要归档时才设置为100%
			if finalProgress >= 99.0 {
				// 进度接近100%，认为归档完成
				Progress[JobInfo.CronJob.JobName] = 100.0
				zlog.Debug("【关键点】表归档完成，设置最终进度为100%，表名:", JobInfo.CronJob.JobName)
			} else {
				// 还有数据需要归档，使用实际计算的进度
				Progress[JobInfo.CronJob.JobName] = finalProgress
				zlog.Debug("【关键点】表归档未完成，更新实际进度，表名:", JobInfo.CronJob.JobName, "进度:", finalProgress, "%")
			}
		}
	case "day":
		zlog.Debug("【关键点】开始执行day模式任务:", JobInfo.CronJob.JobName)
		// 只有当进度不存在时才初始化为0，避免重置正在进行的任务
		if _, exists := Progress[JobInfo.CronJob.JobName]; !exists {
			Progress[JobInfo.CronJob.JobName] = 0.0
			zlog.Debug("初始化进度为0，表名:", JobInfo.CronJob.JobName)
		} else {
			zlog.Debug("保持现有进度，表名:", JobInfo.CronJob.JobName, "当前进度:", Progress[JobInfo.CronJob.JobName])
		}
		// 记录任务执行前的进度
		progressBeforeTask := Progress[JobInfo.CronJob.JobName]
		zlog.Debug("【关键点】任务执行前进度:", JobInfo.CronJob.JobName, "进度:", progressBeforeTask, "%")

		d.Start(JobInfo)
		zlog.Debug("【关键点】day模式任务执行完成:", JobInfo.CronJob.JobName)

		// 检查任务执行后的进度
		progressAfterTask := Progress[JobInfo.CronJob.JobName]
		zlog.Debug("【关键点】任务执行后进度:", JobInfo.CronJob.JobName, "进度:", progressAfterTask, "%")

		// 如果任务执行过程中已经设置为100%（比如无数据需要归档），则不再重新计算
		if progressAfterTask == 100.0 {
			zlog.Debug("【关键点】任务已设置为100%完成，原因: 无数据需要归档或数据过新，表名:", JobInfo.CronJob.JobName)
			zlog.Debug("【关键点】保持100%进度，不进行重新计算")
		} else {
			// 任务完成后检查是否真正完成归档
			finalProgress := d.Conn.TaskProgress(JobInfo.CronJob.JobName, JobInfo.JobDetail.DBName)
			zlog.Debug("【关键点】任务完成后进度检查，表名:", JobInfo.CronJob.JobName, "计算进度:", finalProgress, "%")

			// 只有当真正没有数据需要归档时才设置为100%
			if finalProgress >= 99.0 {
				// 进度接近100%，认为归档完成
				Progress[JobInfo.CronJob.JobName] = 100.0
				zlog.Debug("【关键点】表归档完成，设置最终进度为100%，表名:", JobInfo.CronJob.JobName)
			} else {
				// 还有数据需要归档，使用实际计算的进度
				Progress[JobInfo.CronJob.JobName] = finalProgress
				zlog.Debug("【关键点】表归档未完成，更新实际进度，表名:", JobInfo.CronJob.JobName, "进度:", finalProgress, "%")
			}
		}
	case "sync":
		zlog.Debug("【关键点】开始执行sync模式任务:", JobInfo.CronJob.JobName)
		// sync模式不需要进度，直接执行
		s.Start(JobInfo)
		zlog.Debug("【关键点】sync模式任务执行完成:", JobInfo.CronJob.JobName)
		// sync模式完成后进度直接设为100%
		Progress[JobInfo.CronJob.JobName] = 100.0
		zlog.Debug("sync模式任务完成，进度设为100%，表名:", JobInfo.CronJob.JobName)
	default:
		zlog.Error("【错误】未知的执行模式:", JobInfo.JobDetail.CMode, "任务名:", JobInfo.CronJob.JobName)
		fmt.Printf("no action match")
	}
	zlog.Debug("【关键点】Metrics函数执行完成，任务名:", JobInfo.CronJob.JobName)
}

func NewScheduler() *Scheduler {
	return &Scheduler{
		scheduleTable: make(map[string]*JobInfo),
		updateChan:    make(chan map[string]*JobInfo),
		stopChans:     make(map[string]chan struct{}),
	}
}

func (s *Scheduler) ScheduleCrontab(ctx context.Context) {
	zlog.Debug("调度器启动")
	zlog.Debug("调度器初始化完成，开始监听任务更新和上下文取消信号")
	for {
		select {
		case <-ctx.Done():
			zlog.Debug("收到上下文取消信号，开始停止所有任务")
			s.stopAllJobs()
			zlog.Debug("所有任务已停止，调度器结束")
			return
		case newTable := <-s.updateChan:
			zlog.Debug("收到新的调度表更新，任务数量:", len(newTable))
			s.mu.Lock()
			zlog.Debug("获取调度器锁，开始更新调度表")
			s.stopAllJobs()
			zlog.Debug("旧任务已全部停止")
			s.scheduleTable = newTable
			zlog.Debug("调度表已更新，开始启动新任务")
			s.startJobs()
			zlog.Debug("新任务启动完成，释放调度器锁")
			s.mu.Unlock()
		}
		time.Sleep(100 * time.Millisecond)
	}
}

func (s *Scheduler) startJobs() {
	zlog.Debug("开始启动所有调度任务，任务总数:", len(s.scheduleTable))
	for JobName, jobinfo := range s.scheduleTable {
		zlog.Debug("准备启动任务:", JobName, "下次执行时间:", jobinfo.CronJob.NextTime.Format("2006-01-02 15:04:05"))
		stopChan := make(chan struct{})
		s.stopChans[JobName] = stopChan

		go func(jobName string, JobInfo *JobInfo, stopChan chan struct{}) {
			zlog.Debug("任务协程已启动:", jobName)
			for {
				select {
				case <-stopChan:
					zlog.Debug("收到停止信号，任务协程退出:", jobName)
					return
				default:
					now := time.Now()
					// 所有模式的任务都立即执行一次，以便验证
					if JobInfo.CronJob.NextTime.Before(now) {
						zlog.Debug("【关键点】检测到任务需要执行，模式:", JobInfo.JobDetail.CMode, "任务名:", jobName, "当前时间:", now.Format("2006-01-02 15:04:05"))
						zlog.Debug("【任务详情】", JobInfo.JobDetail)
						zlog.Debug("【关键点】强制立即执行任务:", jobName, "模式:", JobInfo.JobDetail.CMode)

						// 立即执行任务
						zlog.Debug("【关键点】调用Metrics函数开始执行任务:", jobName)
						Metrics(JobInfo)

						// 设置下次执行时间为10秒后，便于快速验证
						JobInfo.CronJob.NextTime = now.Add(10 * time.Second)
						zlog.Debug("【关键点】任务执行完成:", jobName, "下次执行时间:", JobInfo.CronJob.NextTime.Format("2006-01-02 15:04:05"))
					} else if JobInfo.CronJob.NextTime.Equal(now) {
						zlog.Debug("任务到达执行时间:", jobName, "当前时间:", now.Format("2006-01-02 15:04:05"))
						zlog.Debug("开始执行任务:", jobName, "模式:", JobInfo.JobDetail.CMode)
						Metrics(JobInfo)
						nextTime := JobInfo.CronJob.Expr.Next(now)
						JobInfo.CronJob.NextTime = nextTime
						zlog.Debug("任务执行完成:", jobName, "下次执行时间:", nextTime.Format("2006-01-02 15:04:05"))
					}
					time.Sleep(100 * time.Millisecond)
				}
			}
		}(JobName, jobinfo, stopChan)
		zlog.Debug("任务启动成功:", JobName)
	}
	zlog.Debug("所有调度任务启动完成")
}

func (s *Scheduler) stopAllJobs() {
	for _, stopChan := range s.stopChans {
		close(stopChan)
	}
	s.stopChans = make(map[string]chan struct{})
}

func (s *Scheduler) UpdateScheduleTable(newTable map[string]*JobInfo) {
	zlog.Debug("更新调度表")
	s.updateChan <- newTable
}
