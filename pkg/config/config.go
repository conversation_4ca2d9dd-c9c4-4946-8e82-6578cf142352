package config

import (
	"encoding/json"
	"flag"
	"fmt"
	"github.com/BurntSushi/toml"
	"github.com/gorhill/cronexpr"
	"github.com/juju/errors"
	"gopkg.in/yaml.v2"
	ldb "mysql_archive/pkg/leveldb"
	"mysql_archive/pkg/network"
	"mysql_archive/pkg/zlog"
	"sync"
	"time"
)

// 10001 查询备份sql失败 QueryBackSql
// 10002 读取表内容失败 ReadRaw
// 10003 连接数据库失败 ConnDatabase
// 10004 表不存在 TableNotExsit
// 10005 创建表失败 CreateTbale
// 10006 结构无法修正 NotAlterColumn
// 10007 克隆表失败 CloneTable
// 10008 初始化数据库失败 InitDatabase
// 10009 连接关闭失败 ConnClose
// 10010 数据库连接断开 DataBaseConnDisconnect
// 10011 重连数据库库失败 ReConnDatabase
// 10012 获取配置失败 ReadConfig
// 10013 查询分组日期失败 QueryLastDate
// 10014 插入失败 Insert
// 10015 删除失败 Del
// 10016 analyze 表失败 AnalyzeTable
// 10017 Post请求失败 UrlPost
// 10018 Get请求失败 UrlGet
// 10019 表锁住 TableLock
// 10020 sql执行错误 SqlAction

// Sync 模式专用状态码 (10021-10040)
// 10021 Sync成功-新建表并同步完成 SyncSuccessNewTable
// 10022 Sync成功-现有表同步完成 SyncSuccessExisting
// 10023 Sync成功-表结构无变化 SyncSuccessNoChanges
// 10024 Sync成功-临时表创建并同步完成 SyncSuccessTempTable
// 10025 Sync失败-临时表清理失败 SyncFailTempCleanup
// 10026 Sync失败-获取表结构失败 SyncFailGetSchema
// 10027 Sync失败-临时表创建失败 SyncFailTempCreate
// 10028 Sync失败-表结构同步失败 SyncFailSchemaSync
// 10029 Sync失败-查询相关表失败 SyncFailQueryTables
// 10030 Sync失败-配置不存在 SyncFailConfigMissing
const (
	NacosUrl = "https://10.33.0.50"
	//NacosUrl               = "http://127.0.0.1:8888"
	QueryBackSql           = 10001
	ReadRaw                = 10002
	ConnDatabase           = 10003
	TableNotExsit          = 10004
	CreateTbale            = 10005
	NotAlterColumn         = 10006
	CloneTable             = 10007
	InitDatabase           = 10008
	ConnClose              = 10009
	DataBaseConnDisconnect = 10010
	ReConnDatabase         = 10011
	ReadConfig             = 10012
	QueryLastDate          = 10013
	Insert                 = 10014
	Del                    = 10015
	AnalyzeTable           = 10016
	UrlPost                = 10017
	TableSchmeFail         = 10018
	TableLock              = 10019
	SqlAction              = 10020

	// Sync 模式专用状态码
	SyncSuccessNewTable   = 10021
	SyncSuccessExisting   = 10022
	SyncSuccessNoChanges  = 10023
	SyncSuccessTempTable  = 10024
	SyncFailTempCleanup   = 10025
	SyncFailGetSchema     = 10026
	SyncFailTempCreate    = 10027
	SyncFailSchemaSync    = 10028
	SyncFailQueryTables   = 10029
	SyncFailConfigMissing = 10030
)

type MysqlBase struct {
	Username string
	Pwd      string
	Ipaddr   string
	Port     string
	Database string
}

// ConfigVersion 配置版本
type ConfigVersion struct {
	// 备份表信息
	backinfo int64
	sql      int64
}

// VersionInfo 版本信息
type VersionInfo struct {
	// 版本字符串
	VersionString string `json:"version_string"`
}

// SyncStatusInfo Sync状态详细信息
type SyncStatusInfo struct {
	// 状态码
	Code int64 `json:"code"`
	// 状态描述
	Message string `json:"message"`
	// 简化状态（保持兼容性）
	Status string `json:"status"`
	// 最后更新时间
	UpdateTime int64 `json:"update_time"`
}

// StatuInfo 状态信息
type StatuInfo struct {
	// 模块状态
	StatusMap map[string]int64
	// 配置版本
	Configversion ConfigVersion `json:"configversion"`
	// 运行开关
	Running bool `json:"running"`
	// 错误码
	Status int64 `json:"status"`
	// 程序版本（保持兼容性）
	Version int64 `json:"version"`
	// 详细版本信息
	VersionInfo VersionInfo `json:"version_info"`
	// Debug日志开关
	DebugLog bool `json:"debug_log"`
}

type JobDetail struct {
	PRIMARY    string
	Batch      int
	CMode      string
	ColumnType string
	Conditions string
	DateColumn string
	Expr       *cronexpr.Expression
	Interval   int
	DBName     string
}

type JobInfo struct {
	JobDetail JobDetail
	DDl       string
	CronJob   *CronJob
}

type CronJob struct {
	JobName  string
	Expr     *cronexpr.Expression
	NextTime time.Time
}

// 版本信息常量
const (
	VERSION_MAJOR = 1
	VERSION_MINOR = 2
	VERSION_PATCH = 0
)

// 构建时注入的变量
var (
	BuildVersion = "dev"
)

// GetVersionInfo 获取版本信息
func GetVersionInfo() VersionInfo {
	versionString := fmt.Sprintf("v%d.%d.%d", VERSION_MAJOR, VERSION_MINOR, VERSION_PATCH)
	if BuildVersion != "dev" {
		versionString = BuildVersion
	}

	return VersionInfo{
		VersionString: versionString,
	}
}

var (
	Progress      = map[string]float64{}
	SyncStatus    = map[string]*SyncStatusInfo{}
	Role          sync.Map
	Backinfo      = map[string]map[string]string{}
	Configversion = &ConfigVersion{0, 0}

	Re = &StatuInfo{
		map[string]int64{},
		*Configversion,
		true,
		0,
		20250715, // 保持兼容性
		GetVersionInfo(),
		false}

	Mysqldb = &MysqlBase{
		Username: "hsyq",
		Pwd:      "Hsyq@123",
		Ipaddr:   "127.0.0.1",
		Port:     "3306",
		Database: "gstation",
	}
	Ldb1 = &ldb.LevelDBStore{}
)

func GetConfig(url string) []byte {
	zlog.Debug("【HTTP请求】==================== 开始HTTP请求 ====================")
	zlog.Debug("【HTTP请求】步骤1: 准备发送HTTP请求")
	zlog.Debug("【HTTP请求】目标URL:", url)

	var R = new(network.Request)
	zlog.Debug("【HTTP请求】步骤2: HTTP客户端创建成功")

	zlog.Debug("【HTTP请求】步骤3: 开始发送HTTPS GET请求")
	StatusCode, body := R.GetHttps(url)
	zlog.Debug("【HTTP请求】步骤4: HTTP请求完成")
	zlog.Debug("【HTTP请求】响应状态码:", StatusCode)
	zlog.Debug("【HTTP请求】响应体长度:", len(body))

	if StatusCode == 200 {
		zlog.Debug("【HTTP请求】步骤5: 请求成功 ")
		zlog.Debug("【HTTP请求】成功获取配置数据，大小:", len(body), "字节")
		if len(body) > 0 {
			// 显示前100个字符作为预览
			preview := string(body)
			zlog.Debug("【HTTP请求】配置内容预览:", preview)
		}
		zlog.Debug("【HTTP请求】==================== HTTP请求成功 ====================")
		return body
	} else {
		zlog.Error("【HTTP请求】步骤5: 请求失败 ")
		zlog.Error("【HTTP请求】失败原因: HTTP状态码异常")
		zlog.Error("【HTTP请求】期望状态码: 200")
		zlog.Error("【HTTP请求】实际状态码:", StatusCode)
		zlog.Error("【HTTP请求】==================== HTTP请求失败 ====================")
	}
	return nil
}

// 备份策略配置
func BackInfo() map[string]map[string]string {
	zlog.Debug("【BackInfo】==================== 开始加载备份配置 ====================")
	var configMap = make(map[string]map[string]string)
	if Backinfo, ok := Role.Load("backupinfo"); ok {
		// 检查类型，确保是[]byte类型的配置数据
		if backinfoBytes, isByteSlice := Backinfo.([]byte); isByteSlice {
			zlog.Info("【BackInfo】从Role缓存加载配置成功，配置长度:", len(backinfoBytes))
			err := yaml.Unmarshal(backinfoBytes, configMap)
			if err != nil {
				Re.Status = QueryBackSql
				Re.StatusMap["BackInfo"] = 1
				zlog.Error("【BackInfo】解析备份配置YAML失败:", err.Error())
				zlog.Error("【BackInfo】原始配置内容:", string(backinfoBytes))
				zlog.Error("【BackInfo】==================== 配置加载失败 ====================")
				return nil
			} else {
				Re.Status = QueryBackSql
				Re.StatusMap["BackInfo"] = 0
				zlog.Debug("【BackInfo】备份配置解析成功 ")
				zlog.Debug("【BackInfo】配置项数量:", len(configMap))
				zlog.Debug("【BackInfo】==================== 配置加载成功 ====================")
				return configMap
			}
		} else {
			// 缓存中的值不是[]byte类型
			zlog.Error("【BackInfo】缓存中的值类型错误")
			zlog.Error("【BackInfo】键 backupinfo 对应的值不是配置数据（[]byte类型）")
			zlog.Error("【BackInfo】实际类型:", fmt.Sprintf("%T", Backinfo))
			Re.Status = QueryBackSql
			Re.StatusMap["BackInfo"] = 1
			return nil
		}
	} else {
		Re.Status = QueryBackSql
		zlog.Error("【BackInfo】从Role缓存加载配置失败，backupinfo不存在")
		zlog.Error("【BackInfo】==================== 配置加载失败 ====================")
	}
	return configMap
}

func PutStatusInfo(key string, info StatuInfo) error {
	data, err := json.Marshal(info)
	if err != nil {
		return err
	}
	return Ldb1.KvPut(key, data)
}

func GetStatusInfo(key string) (StatuInfo, error) {
	data, err := Ldb1.KvGet(key)
	if err != nil {
		return StatuInfo{}, err
	}
	var info StatuInfo
	err = json.Unmarshal([]byte(data), &info)
	if err != nil {
		return StatuInfo{}, err
	}
	return info, nil
}

// SetSyncStatus 设置Sync状态
func SetSyncStatus(tableName string, code int64, message string) {
	status := "fail"
	if code >= 10021 && code <= 10024 { // 成功状态码范围
		status = "ok"
	}

	SyncStatus[tableName] = &SyncStatusInfo{
		Code:       code,
		Message:    message,
		Status:     status,
		UpdateTime: time.Now().Unix(),
	}

	zlog.Debug("设置Sync状态，表名:", tableName, "状态码:", code, "消息:", message, "状态:", status)
}

// GetSyncStatusMessage 根据状态码获取消息
func GetSyncStatusMessage(code int64) string {
	messages := map[int64]string{
		SyncSuccessNewTable:   "新建表并同步完成",
		SyncSuccessExisting:   "现有表同步完成",
		SyncSuccessNoChanges:  "表结构无变化",
		SyncSuccessTempTable:  "临时表创建并同步完成",
		SyncFailTempCleanup:   "临时表清理失败",
		SyncFailGetSchema:     "获取表结构失败",
		SyncFailTempCreate:    "临时表创建失败",
		SyncFailSchemaSync:    "表结构同步失败",
		SyncFailQueryTables:   "查询相关表失败",
		SyncFailConfigMissing: "配置不存在",
	}

	if msg, ok := messages[code]; ok {
		return msg
	}
	return fmt.Sprintf("未知状态码: %d", code)
}

// 配置文件

type Config struct {
	*flag.FlagSet `json:"-"`
	ConfigFile    string       `toml:"-" json:"-"`
	PadderConfig  PadderConfig `toml:"padder" json:"padder"`
	Version       bool
}

type PadderConfig struct {
	MySQLConfig *MySQLConfig `toml:"mysql" json:"mysql"`
}

func (cfg *Config) ParseCmd(arguments []string) error {
	return cfg.FlagSet.Parse(arguments)
}

type MySQLConfig struct {
	Target *DBConfig `toml:"target" json:"target"`
}

func (c *Config) CreateConfigFromFile(path string) error {
	_, err := toml.DecodeFile(path, c)
	return errors.Trace(err)
}

func CreateConfigFromString(configString string) (*Config, error) {
	cfg := &Config{}
	_, err := toml.Decode(configString, cfg)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return cfg, nil
}

func Validate(cfg PadderConfig) error {
	if cfg.MySQLConfig == nil {
		return errors.NotValidf("mysql config is required. config is")
	}
	if cfg.MySQLConfig.Target == nil {
		return errors.NotValidf("mysql target is required. config is")
	}
	if err := cfg.MySQLConfig.Target.ValidateAndSetDefault(); err != nil {
		return errors.NotValidf("mysql config validates failed. %v, config is", err)
	}
	if cfg.MySQLConfig.Target.Schema == "" {
		return errors.NotValidf("mysql schema is required. config is")
	}
	return nil
}
