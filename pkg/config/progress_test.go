package config

import (
	"sync"
	"testing"
	"time"
)

// TestProgressConcurrentAccess 测试Progress map的并发访问安全性
func TestProgressConcurrentAccess(t *testing.T) {
	// 重置Progress map
	Progress = make(map[string]float64)
	
	const numGoroutines = 100
	const numOperations = 1000
	
	var wg sync.WaitGroup
	
	// 启动多个goroutine进行并发读写操作
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			tableName := "test_table"
			
			for j := 0; j < numOperations; j++ {
				// 随机进行不同类型的操作
				switch j % 4 {
				case 0:
					// 设置进度
					SetProgress(tableName, float64(j%101))
				case 1:
					// 获取进度
					GetProgress(tableName)
				case 2:
					// 更新进度
					UpdateProgress(tableName, float64(j%101))
				case 3:
					// 检查进度是否存在
					CheckProgressExists(tableName)
				}
			}
		}(i)
	}
	
	// 同时启动一个goroutine进行GetAllProgress操作
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < numOperations; i++ {
			GetAllProgress()
			time.Sleep(time.Microsecond)
		}
	}()
	
	// 等待所有goroutine完成
	wg.Wait()
	
	// 验证最终状态
	finalProgress := GetProgress("test_table")
	if finalProgress < 0 || finalProgress > 100 {
		t.Errorf("最终进度值异常: %f", finalProgress)
	}
}

// TestProgressThreadSafety 测试Progress函数的线程安全性
func TestProgressThreadSafety(t *testing.T) {
	// 重置Progress map
	Progress = make(map[string]float64)
	
	const numTables = 10
	const numGoroutines = 50
	
	var wg sync.WaitGroup
	
	// 为每个表启动多个goroutine进行并发操作
	for tableId := 0; tableId < numTables; tableId++ {
		for goroutineId := 0; goroutineId < numGoroutines; goroutineId++ {
			wg.Add(1)
			go func(tId, gId int) {
				defer wg.Done()
				
				tableName := "table_" + string(rune('0'+tId))
				
				// 初始化进度
				InitProgress(tableName, 0.0)
				
				// 逐步更新进度
				for progress := 1.0; progress <= 100.0; progress += 1.0 {
					UpdateProgress(tableName, progress)
					time.Sleep(time.Microsecond)
				}
			}(tableId, goroutineId)
		}
	}
	
	// 启动一个监控goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < 1000; i++ {
			allProgress := GetAllProgress()
			for tableName, progress := range allProgress {
				if progress < 0 || progress > 100 {
					t.Errorf("表 %s 进度值异常: %f", tableName, progress)
				}
			}
			time.Sleep(time.Microsecond)
		}
	}()
	
	wg.Wait()
	
	// 验证最终状态
	allProgress := GetAllProgress()
	if len(allProgress) != numTables {
		t.Errorf("期望 %d 个表，实际得到 %d 个", numTables, len(allProgress))
	}
	
	for tableName, progress := range allProgress {
		if progress != 100.0 {
			t.Errorf("表 %s 最终进度应该是 100.0，实际是 %f", tableName, progress)
		}
	}
}

// TestProgressUpdateLogic 测试进度更新逻辑的正确性
func TestProgressUpdateLogic(t *testing.T) {
	// 重置Progress map
	Progress = make(map[string]float64)
	
	tableName := "test_update_logic"
	
	// 测试初始化
	if InitProgress(tableName, 10.0) != true {
		t.Error("初始化进度应该成功")
	}
	
	if GetProgress(tableName) != 10.0 {
		t.Error("初始化进度值不正确")
	}
	
	// 测试重复初始化
	if InitProgress(tableName, 20.0) != false {
		t.Error("重复初始化应该失败")
	}
	
	if GetProgress(tableName) != 10.0 {
		t.Error("重复初始化不应该改变进度值")
	}
	
	// 测试进度更新（只允许增长）
	if UpdateProgress(tableName, 50.0) != true {
		t.Error("进度增长更新应该成功")
	}
	
	if GetProgress(tableName) != 50.0 {
		t.Error("进度更新值不正确")
	}
	
	// 测试进度回退（应该被拒绝）
	if UpdateProgress(tableName, 30.0) != false {
		t.Error("进度回退应该被拒绝")
	}
	
	if GetProgress(tableName) != 50.0 {
		t.Error("进度回退不应该改变进度值")
	}
	
	// 测试无效进度值
	if UpdateProgress(tableName, -10.0) != false {
		t.Error("负数进度应该被拒绝")
	}
	
	if UpdateProgress(tableName, 150.0) != false {
		t.Error("超过100的进度应该被拒绝")
	}
	
	// 测试直接设置
	SetProgress(tableName, 75.0)
	if GetProgress(tableName) != 75.0 {
		t.Error("直接设置进度值不正确")
	}
}

// BenchmarkProgressConcurrentAccess 性能基准测试
func BenchmarkProgressConcurrentAccess(b *testing.B) {
	// 重置Progress map
	Progress = make(map[string]float64)
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		tableName := "bench_table"
		counter := 0
		for pb.Next() {
			switch counter % 3 {
			case 0:
				SetProgress(tableName, float64(counter%101))
			case 1:
				GetProgress(tableName)
			case 2:
				UpdateProgress(tableName, float64(counter%101))
			}
			counter++
		}
	})
}
